请求参数

下表仅列出该接口特有的请求参数和部分公共参数。更多信息请见公共参数。

| 参数 | 类型 | 是否必填 | 示例值 | 描述 |
| --- | --- | --- | --- | --- |
| Action | String | 是 | ListFoundationModelVersions | 要执行的操作，取值：ListFoundationModelVersions。 |
| Version | String | 是 | 2024-01-01 | API的版本，取值：2024-01-01。 |
| FoundationModelName | String | 是 | test-foundation-model | 基础模型名称 |
| PageNumber | Integer | 否 | 1 | 分页查询时的起始页码，从1开始，默认为1 |
| PageSize | Integer | 否 | 1 | 分页查询时每页显示的记录数，取值：·最小值：1·最大值：100·默认值：10 |
| +Filter | Object | 否 | - | 待查询基础模型版本的筛选条件 |
| SortOrder | String | 否 | Desc | 指定排序顺序。可指定值：·Asc：升序排列·Desc：降序排列 |
| SortBy | String | 否 | - | 指定排序指标。可指定值：·CreateTime创建时间·UpdateTime更新时间 |



返回参数

下表仅列出本接口特有的返回参数。更多信息请参见返回结构。

| 参数 | 类型 | 示例值 | 描述 |
| --- | --- | --- | --- |
| TotalCount | Integer | 2 | 总基础模型版本数 |
| PageNumber | Integer | 1 | 分页查询时的起始页码，从1开始，默认为1 |
| PageSize | Integer | 1 | 分页查询时每页显示的记录数，取值：·最小值：1·最大值：100·默认值：10 |
| +Items | Array of Object | - | 基础模型版本列表 |


请求示例
POST /?Action=ListFoundationModelVersions&Version=2024-01-01 HTTP/1.1
Host: open.volcengineapi.com
Content-Type: application/json; charset=UTF-8
X-Date: 20240514T130104Z
X-Content-Sha256: 287e874e******d653b44d21e
Authorization: HMAC-SHA256 Credential=Adfks******wekfwe/20240514/cn-beijing/ark/request, SignedHeaders=host;x-content-sha256;x-date, Signature=47a7d934ff7b37c03938******cd7b8278a40a1057690c401e92246a0e41085f

{
  "FoundationModelName": "test-foundation-model",
  "PageNumber": 1,
  "PageSize": 10,
  "Filter": {
    "Description": "WU",
    "ModelVersions": [
      "783"
    ],
    "Statuses": [
      "M0"
    ]
  },
  "SortOrder": "Desc",
  "SortBy": "CreateTime"
}



返回示例
{
  "ResponseMetadata": {
    "RequestId": "2024051421010908503002501682D9A8",
    "Action": "ListFoundationModelVersions",
    "Version": "2024-01-01",
    "Service": "ark",
    "Region": "cn-beijing"
  },
  "Result": {
    "TotalCount": 2,
    "PageNumber": 1,
    "PageSize": 1,
    "Items": [
      {
        "FoundationModelName": "test-foundation-model",
        "ModelVersion": "1.0",
        "Description": "r",
        "ActiveConfigurationId": "93RniLBUHsU",
        "Status": "Kq7b",
        "PublishTime": "2006-01-02T15:04:05Z07:00\n",
        "CreateTime": "2006-01-02T15:04:05Z07:00\n",
        "UpdateTime": "2006-01-02T15:04:05Z07:00\n"
      }
    ]
  }
}

错误码
401

Unauthorized

AuthenticationError

The API key or AK/SK in the request is missing or invalid. Request ID: {id}

请求携带的 API Key 或 AK/SK 校验未通过，请您重新检查设置的 鉴权凭证，或者查看 API 调用文档来排查问题。